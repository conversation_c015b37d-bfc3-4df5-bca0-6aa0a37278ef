const request = require('supertest');
const app = require('../../src/app');
const { User } = require('../../src/models');

describe('AuthController', () => {
  beforeEach(async () => {
    // Clean database before each test
    await User.destroy({ where: {}, truncate: true });
  });

  describe('POST /api/v1/auth/login', () => {
    describe('successful login', () => {
      it('should return 200 and auth token for valid credentials', async () => {
        // Arrange - Create a test user
        const testUser = await User.create({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'password123',
          role: 'user',
        });

        const loginData = {
          email: '<EMAIL>',
          password: 'password123',
        };

        // Act
        const response = await request(app).post('/api/v1/auth/login').send(loginData);

        // Assert
        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(response.body.data).toHaveProperty('authToken');
        expect(response.body.data).toHaveProperty('user');
        expect(response.body.data.user).toMatchObject({
          id: testUser.id,
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user',
        });
        expect(typeof response.body.data.authToken).toBe('string');
        expect(response.body.data.authToken.length).toBeGreaterThan(0);
      });

      it('should handle email case normalization', async () => {
        // Arrange - Create a test user with lowercase email
        await User.create({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'password123',
          role: 'admin',
        });

        const loginData = {
          email: '<EMAIL>',
          password: 'password123',
        };

        // Act
        const response = await request(app).post('/api/v1/auth/login').send(loginData);

        // Assert
        expect(response.status).toBe(200);
        expect(response.body.data.user.email).toBe('<EMAIL>');
      });
    });

    describe('validation errors', () => {
      it('should return 400 for missing email', async () => {
        // Arrange
        const loginData = { password: 'password123' };

        // Act
        const response = await request(app).post('/api/v1/auth/login').send(loginData);

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
        expect(response.body).toHaveProperty('details');
        expect(Array.isArray(response.body.details)).toBe(true);
      });

      it('should return 400 for missing password', async () => {
        // Arrange
        const loginData = { email: '<EMAIL>' };

        // Act
        const response = await request(app).post('/api/v1/auth/login').send(loginData);

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
        expect(response.body).toHaveProperty('details');
        expect(Array.isArray(response.body.details)).toBe(true);
      });

      it('should return 400 for invalid email format', async () => {
        // Arrange
        const loginData = {
          email: 'invalid-email',
          password: 'password123',
        };

        // Act
        const response = await request(app).post('/api/v1/auth/login').send(loginData);

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
        expect(response.body).toHaveProperty('details');
        expect(Array.isArray(response.body.details)).toBe(true);
      });
    });

    describe('authentication errors', () => {
      it('should return 401 for non-existent user', async () => {
        // Arrange - No user created, so login should fail
        const loginData = {
          email: '<EMAIL>',
          password: 'password123',
        };

        // Act
        const response = await request(app).post('/api/v1/auth/login').send(loginData);

        // Assert
        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Invalid email or password');
      });

      it('should return 401 for invalid password', async () => {
        // Arrange - Create user with known password
        await User.create({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'correctpassword',
          role: 'user',
        });

        const loginData = {
          email: '<EMAIL>',
          password: 'wrongpassword',
        };

        // Act
        const response = await request(app).post('/api/v1/auth/login').send(loginData);

        // Assert
        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Invalid email or password');
      });
    });

    describe('edge cases', () => {
      it('should handle empty request body', async () => {
        // Act
        const response = await request(app).post('/api/v1/auth/login').send({});

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
        expect(response.body).toHaveProperty('details');
        expect(Array.isArray(response.body.details)).toBe(true);
        expect(response.body.details.length).toBeGreaterThan(0);
      });

      it('should handle malformed JSON', async () => {
        // Act
        const response = await request(app)
          .post('/api/v1/auth/login')
          .set('Content-Type', 'application/json')
          .send('{"email": "<EMAIL>", "password":}'); // Invalid JSON

        // Assert
        expect(response.status).toBe(400);
      });
    });
  });
});
